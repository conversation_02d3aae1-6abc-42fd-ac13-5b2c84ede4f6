import { useState, useCallback } from 'react';

export function useNodePopupMenu() {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [open, setOpen] = useState(false);
  const [nodeType, setNodeType] = useState<string | null>(null);

  const handleOpen = useCallback((event: React.MouseEvent<HTMLElement>, type: string) => {
    setAnchorEl(event.currentTarget);
    setNodeType(type);
    setOpen(true);
  }, []);

  const handleClose = useCallback(() => {
    setAnchorEl(null);
    setOpen(false);
  }, []);

  const handleToggle = useCallback((event: React.MouseEvent<HTMLElement>, type: string) => {
    if (!open) {
      handleOpen(event, type);
    } else {
      handleClose();
    }
  }, [open, handleOpen, handleClose]);

  return { 
    anchorEl, 
    open, 
    nodeType, 
    handleOpen, 
    handleClose, 
    handleToggle 
  };
}

export default useNodePopupMenu;
