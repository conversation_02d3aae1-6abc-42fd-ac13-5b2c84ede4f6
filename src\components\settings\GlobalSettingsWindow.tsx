import React from 'react';
import { styled } from '@mui/material/styles';
import CloseIcon from '@mui/icons-material/Close';

interface GlobalSettingsWindowProps {
  open: boolean;
  onClose: () => void;
}

const Overlay = styled('div')({
  position: 'fixed',
  top: 0,
  left: 0,
  width: '100vw',
  height: '100vh',
  background: 'rgba(20,24,38,0.75)',
  zIndex: 2000,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backdropFilter: 'blur(6px)',
});

const Modal = styled('div')({
  minWidth: 420,
  maxWidth: 540,
  width: '90vw',
  background: 'linear-gradient(120deg, #22263b 80%, #4e8cff 140%)',
  borderRadius: 18,
  boxShadow: '0 8px 48px 0 rgba(50,100,255,0.18)',
  padding: '36px 34px 30px 34px',
  color: '#f3f6fa',
  position: 'relative',
  display: 'flex',
  flexDirection: 'column',
  gap: 24,
});

const ModalHeader = styled('div')({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  fontWeight: 700,
  fontSize: 22,
  letterSpacing: 0.2,
});

const CloseButton = styled('button')({
  background: 'none',
  border: 'none',
  color: '#fff',
  fontSize: 24,
  cursor: 'pointer',
  display: 'flex',
  alignItems: 'center',
  padding: 2,
  borderRadius: 6,
  transition: 'background 0.18s',
  ':hover': {
    background: 'rgba(80,140,255,0.12)',
  },
});

const Section = styled('div')({
  marginTop: 8,
  display: 'flex',
  flexDirection: 'column',
  gap: 14,
});

const SectionTitle = styled('div')({
  fontWeight: 600,
  fontSize: 16.5,
  marginBottom: 3,
});

const SectionContent = styled('div')({
  fontSize: 15.5,
  color: '#c3c6d1',
});

export const GlobalSettingsWindow: React.FC<GlobalSettingsWindowProps> = ({ open, onClose }) => {
  if (!open) return null;
  return (
    <Overlay>
      <Modal>
        <ModalHeader>
          Global Settings
          <CloseButton onClick={onClose} title="Close">
            <CloseIcon style={{ fontSize: 26 }} />
          </CloseButton>
        </ModalHeader>
        <Section>
          <SectionTitle>API Key</SectionTitle>
          <SectionContent>
            <label style={{ display: 'flex', flexDirection: 'column', gap: 8, fontWeight: 500 }}>
              <span style={{ marginBottom: 2 }}>API Key</span>
              <input
                type="password"
                placeholder="Enter your API key"
                style={{
                  padding: '10px 12px',
                  borderRadius: 7,
                  border: '1.5px solid #3a3e48',
                  background: '#23263a',
                  color: '#e0e6f5',
                  fontSize: 15,
                  outline: 'none',
                  letterSpacing: 1.1,
                  marginTop: 2
                }}
                disabled
              />
            </label>
          </SectionContent>
        </Section>
        <Section>
          <SectionTitle>Personal Information</SectionTitle>
          <SectionContent>
            <label style={{ display: 'flex', alignItems: 'center', gap: 10, fontWeight: 500 }}>
              <input type="checkbox" style={{ accentColor: '#4e8cff', width: 18, height: 18 }} disabled />
              Use personal information as default
            </label>
          </SectionContent>
        </Section>
        <Section>
          <SectionTitle>Theme</SectionTitle>
          <SectionContent>Dark (default)</SectionContent>
        </Section>
        <Section>
          <SectionTitle>Account</SectionTitle>
          <SectionContent>Not signed in</SectionContent>
        </Section>
        <Section>
          <SectionTitle>Language</SectionTitle>
          <SectionContent>English</SectionContent>
        </Section>
        {/* Add more settings sections here as needed */}
      </Modal>
    </Overlay>
  );
};

export default GlobalSettingsWindow;
