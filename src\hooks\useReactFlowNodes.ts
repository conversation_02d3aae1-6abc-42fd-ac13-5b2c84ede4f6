import { useCallback } from 'react';
import { type Node } from '@xyflow/react';
import { GetNodeId } from '../utils/GetNodeId';
import type { BaseNodeData } from '../types/baseNode.types';
import type { ReactFlowNodeData } from '../components/flow/nodes/ReactFlowNodes';

type NodeType = 'input' | 'agent' | 'tool' | 'output';

const nodePresets: Record<NodeType, { label: string; accentColor: string }> = {
  input: { label: 'Input', accentColor: '#4e8cff' },
  agent: { label: 'Agent', accentColor: '#47c78a' },
  tool: { label: 'Tool', accentColor: '#f8b84e' },
  output: { label: 'Output', accentColor: '#ff6978' },
};

/**
 * Hook for adding nodes to React Flow
 */
export function useReactFlowAddNode(
  setNodes: React.Dispatch<React.SetStateAction<Node<ReactFlowNodeData>[]>>
) {
  const addNode = useCallback((type: NodeType, position?: { x: number; y: number }) => {
    const { label, accentColor } = nodePresets[type];
    
    const newBaseNodeData: BaseNodeData = {
      NodeId: GetNodeId(),
      NodeType: type,
      label,
      position: position || { x: Math.random() * 400 + 100, y: Math.random() * 300 + 100 },
      accentColor,
    };

    const newReactFlowNode: Node<ReactFlowNodeData> = {
      id: newBaseNodeData.NodeId,
      type: newBaseNodeData.NodeType,
      position: newBaseNodeData.position,
      data: {
        ...newBaseNodeData,
        label: newBaseNodeData.label,
        accentColor: newBaseNodeData.accentColor,
      },
      dragHandle: '.react-flow__node',
    };

    setNodes((nodes) => [...nodes, newReactFlowNode]);
  }, [setNodes]);

  return addNode;
}

/**
 * Hook for managing React Flow nodes with BaseNodeData compatibility
 */
export function useReactFlowNodeManagement() {
  // This hook can be extended with additional node management functionality
  // such as node selection, deletion, duplication, etc.
  
  const deleteNode = useCallback((
    nodeId: string,
    setNodes: React.Dispatch<React.SetStateAction<Node<ReactFlowNodeData>[]>>
  ) => {
    setNodes((nodes) => nodes.filter(node => node.id !== nodeId));
  }, []);

  const duplicateNode = useCallback((
    nodeId: string,
    nodes: Node<ReactFlowNodeData>[],
    setNodes: React.Dispatch<React.SetStateAction<Node<ReactFlowNodeData>[]>>
  ) => {
    const nodeToDuplicate = nodes.find(node => node.id === nodeId);
    if (!nodeToDuplicate) return;

    const newNode: Node<ReactFlowNodeData> = {
      ...nodeToDuplicate,
      id: GetNodeId(),
      position: {
        x: nodeToDuplicate.position.x + 50,
        y: nodeToDuplicate.position.y + 50,
      },
      data: {
        ...nodeToDuplicate.data,
        NodeId: GetNodeId(),
      },
    };

    setNodes((nodes) => [...nodes, newNode]);
  }, []);

  return {
    deleteNode,
    duplicateNode,
  };
}
