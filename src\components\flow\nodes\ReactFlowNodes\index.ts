// Import components first
import { ReactFlowInputNode } from './ReactFlowInputNode';
import { ReactFlowAgentNode } from './ReactFlowAgentNode';
import { ReactFlowToolNode } from './ReactFlowToolNode';
import { ReactFlowOutputNode } from './ReactFlowOutputNode';

// Export components
export { ReactFlowBaseNode } from './ReactFlowBaseNode';
export { ReactFlowInputNode } from './ReactFlowInputNode';
export { ReactFlowAgentNode } from './ReactFlowAgentNode';
export { ReactFlowToolNode } from './ReactFlowToolNode';
export { ReactFlowOutputNode } from './ReactFlowOutputNode';

export type { ReactFlowNodeData, ReactFlowBaseNodeProps } from './ReactFlowBaseNode';

// Node types mapping for React Flow
export const nodeTypes = {
  input: ReactFlowInputNode,
  agent: ReactFlowAgentNode,
  tool: ReactFlowToolNode,
  output: React<PERSON>lowOutputNode,
};
