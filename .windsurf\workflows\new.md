---
description: Checklist and workflow for creating a new React component
---

# 🚀 Workflow: Create a New React UI Component

Follow this checklist every time you add a new React component to ensure consistency, modularity, and best practices.

1. Ask for details:
Ask what type of component it should be (example: menu, button, background, toolbar)
Ask how the user will access this component in the application or if it's part of a specific section in the application (example: click the first button in the main menu).
Ask if it should follow some styling theme.
Ask more relevant questions if needed

2. Placement & Naming:
Place the file in a appropriate folder inside the `components/` according to the project structure.
Use PascalCase for component and file names (e.g. `MyComponent.tsx`).

3. Use the right styling:
Use Material UI for styling components (avoid global styles unless necessary).


4. Modularity:
Avoid logic inside the UI component. Move logic to hooks or service files unless UI-specific.
Use dependency injection (props/context) for dependencies, not direct imports of logic.

5. Readability:
Use clear and descriptive names for variables, functions, and components.
Add a short doc comment/JSDoc for the component and complex props.
 Keep the file and component small; split if it grows too large.

6. React Best Practices:
Use hooks for state/effects (no class components).
Clean up side effects in `useEffect` if used.
Use unique keys for lists.
Avoid direct DOM manipulation (except via refs).

7. Create the component and ask for feedback

8. Fix component based on feedback (if any feedback is recived)

