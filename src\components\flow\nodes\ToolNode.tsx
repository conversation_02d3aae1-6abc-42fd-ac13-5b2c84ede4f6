import React from 'react';
import { BaseNode } from './BaseNode/BaseNode';
import type { BaseNodeData } from '../../../types/baseNode.types';

interface ToolNodeProps extends Omit<BaseNodeData, 'NodeType' | 'label' | 'accentColor'> {
  NodeId: string;
  position: { x: number; y: number };
  onMouseDown: (e: React.MouseEvent, id: string) => void;
}

export const ToolNode: React.FC<ToolNodeProps> = ({
  NodeId,
  position,
  onMouseDown,
  ...props
}) => (
  <BaseNode
    NodeId={NodeId}
    position={position}
    onMouseDown={onMouseDown}
    NodeType="tool"
    label="Tool"
    accentColor="#f8b84e"
    {...props}
  />
);
