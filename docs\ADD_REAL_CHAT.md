# How to Add a Real Chat Function

This guide explains (like you're 10 years old!) how to make your chat UI talk to a real backend and show real answers.

## 1. Is the chat modular?
Yes! Your chat UI is split into small, reusable pieces:
- **ChatWindow**: The big box that shows the chat.
- **MessageList**: Shows all the messages.
- **MessageInput**: Where you type and send your message.
- **Message**: A single chat bubble.

Each part does only one thing. This makes it super easy to add real chat logic!

---

## 2. Where do I add real chat logic?
You add logic in `ChatWindow.tsx` and `MessageInput.tsx`.

### Example: Add real chat (pseudo-code)

```tsx
// ChatWindow.tsx (only the important part)
const [messages, setMessages] = useState([ /* ... */ ]);

// This function sends your message to the backend
const sendMessage = async (text) => {
  setMessages([...messages, { text, sender: 'user' }]);
  // 1. Send to backend (API call)
  const response = await fetch('/api/chat', {
    method: 'POST',
    body: JSON.stringify({ message: text }),
    headers: { 'Content-Type': 'application/json' }
  });
  const data = await response.json();
  // 2. Add backend's answer to the chat
  setMessages(msgs => [...msgs, { text: data.answer, sender: 'agent' }]); // {OUTPUT}
};

// Pass sendMessage to MessageInput:
<MessageInput onSend={sendMessage} />
```

```tsx
// MessageInput.tsx (only the important part)
const handleSubmit = (e) => {
  e.preventDefault();
  if (message.trim()) {
    onSend(message); // This calls sendMessage in ChatWindow
    setMessage('');
  }
};
```

## 3. Where does the backend answer go?
- The line with `{OUTPUT}` is where you add the backend's answer to the chat.
- It shows up in the chat window for the user to see!

---

## 4. Summary
- The chat UI is modular and ready for real chat.
- Just add an API call in `sendMessage` and show the answer in the messages state.
- Mark the backend answer with `{OUTPUT}` as shown above.

That's it! If you want a more detailed example, just ask!
