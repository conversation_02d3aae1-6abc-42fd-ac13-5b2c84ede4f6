import React from 'react';
import { React<PERSON>lowBaseNode, type ReactFlowBaseNodeProps } from './ReactFlowBaseNode';

export const ReactFlowToolNode: React.FC<ReactFlowBaseNodeProps> = (props) => {
  const nodeData = {
    ...props.data,
    label: 'Tool',
    accentColor: '#f8b84e',
    NodeType: 'tool' as const
  };

  return (
    <ReactFlowBaseNode
      data={nodeData}
    >
      {/* Tool node specific content can go here */}
    </ReactFlowBaseNode>
  );
};
