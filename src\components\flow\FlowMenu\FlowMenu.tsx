import React from 'react';
import { styled } from '@mui/material/styles';

import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import BuildIcon from '@mui/icons-material/Build';


const MenuContainer = styled('div')(() => ({
  position: 'absolute',
  right: 0,
  top: 0,
  height: '100%',
  width: 280,
  background: 'rgba(30, 32, 38, 0.85)', // glassy
  color: '#f3f6fa',
  boxShadow: '-4px 0 24px 0 rgba(0,0,0,0.18)',
  backdropFilter: 'blur(14px)',
  borderTopLeftRadius: 18,
  borderBottomLeftRadius: 18,
  zIndex: 10,
  display: 'flex',
  flexDirection: 'column',
  transition: 'transform 0.3s cubic-bezier(.4,1.2,.6,1)',
  borderLeft: '1.5px solid #3a3e48',
}));

const MenuHandle = styled('div')({
  position: 'absolute',
  right: 0,
  top: '40%',
  width: 30,
  height: 70,
  background: 'linear-gradient(120deg, #2f3549 60%, #4e8cff 100%)',
  borderTopLeftRadius: 14,
  borderBottomLeftRadius: 14,
  boxShadow: '-4px 0 16px rgba(50,100,255,0.11)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  cursor: 'pointer',
  zIndex: 20,
  border: '1.5px solid #4e8cff',
  transition: 'background 0.2s',
  '&:hover': {
    background: 'linear-gradient(120deg, #4e8cff 70%, #6fc3ff 100%)',
  }
});

const MenuHeader = styled('div')({
  display: 'flex',
  alignItems: 'center',
  gap: 10,
  padding: '22px 26px 18px 26px',
  fontWeight: 700,
  fontSize: 20,
  borderBottom: '1.5px solid #353945',
  background: 'linear-gradient(110deg, #23272f 80%, #4e8cff 120%)',
  color: '#f3f6fa',
  cursor: 'pointer',
  userSelect: 'none',
  boxShadow: '0 2px 8px 0 rgba(78,140,255,0.05)',
  letterSpacing: 0.3,
});

const Section = styled('div')({
  padding: '20px 26px 12px 26px',
  borderBottom: '1.5px solid #2d2f36',
  marginBottom: 4,
});

const SectionTitle = styled('div')({
  fontWeight: 600,
  fontSize: 15.5,
  marginBottom: 12,
  color: '#4e8cff',
  textTransform: 'uppercase',
  letterSpacing: 0.7,
});

const MenuList = styled('ul')({
  listStyle: 'none',
  padding: 0,
  margin: 0,
  display: 'flex',
  flexDirection: 'column',
  gap: 6,
});

const MenuItem = styled('li')({
  padding: '11px 0 11px 12px',
  cursor: 'pointer',
  borderRadius: 8,
  fontWeight: 500,
  fontSize: 16,
  color: '#e0e6f5',
  transition: 'background 0.18s, color 0.18s',
  border: '1.5px solid transparent',
  boxShadow: '0 1px 4px 0 rgba(78,140,255,0.03)',
  '&:hover': {
    background: 'linear-gradient(90deg, #26304a 60%, #4e8cff 120%)',
    color: '#fff',
    border: '1.5px solid #4e8cff',
  },
  '&:active': {
    background: 'linear-gradient(90deg, #4e8cff 70%, #6fc3ff 100%)',
    color: '#fff',
    border: '1.5px solid #6fc3ff',
  },
});

export interface FlowMenuProps {
  expanded: boolean;
  onToggle: () => void;
  onAddNode: (type: string) => void;
}

const FlowMenu: React.FC<FlowMenuProps> = ({ expanded, onToggle, onAddNode }) => {
  return (
    <>
      <MenuContainer style={{ transform: expanded ? 'translateX(0)' : 'translateX(100%)' }}>
        <MenuHeader>
          Agent Flow Menu
        </MenuHeader>
        {expanded && <>
          <Section>
            <SectionTitle>Nodes</SectionTitle>
            <MenuList>
              <MenuItem onClick={() => onAddNode('input')}>
                <ArrowDownwardIcon sx={{ fontSize: 20, color: '#2196f3', verticalAlign: 'middle', mr: 1 }} />
                Input
              </MenuItem>
              <MenuItem onClick={() => onAddNode('agent')}>
                <SmartToyIcon sx={{ fontSize: 20, color: '#2196f3', verticalAlign: 'middle', mr: 1 }} />
                Agent
              </MenuItem>
              <MenuItem onClick={() => onAddNode('tool')}>
                <BuildIcon sx={{ fontSize: 20, color: '#2196f3', verticalAlign: 'middle', mr: 1 }} />
                Tool
              </MenuItem>
              <MenuItem onClick={() => onAddNode('output')}>
                <ArrowUpwardIcon sx={{ fontSize: 20, color: '#2196f3', verticalAlign: 'middle', mr: 1 }} />
                Output
              </MenuItem>
            </MenuList>
          </Section>
          <Section>
            <SectionTitle>Templates</SectionTitle>
            <MenuList>
              <MenuItem>Basic Chain</MenuItem>
              <MenuItem>Multi-Agent</MenuItem>
            </MenuList>
          </Section>
        </>}

      </MenuContainer>
      <MenuHandle
        onClick={onToggle}
        style={{
          right: expanded ? 280 : 0, // menu width
          transition: 'right 0.3s cubic-bezier(.4,1.2,.6,1)',
        }}
      >
        {expanded ? (
          <ChevronRightIcon style={{ color: '#fff' }} />
        ) : (
          <ChevronLeftIcon style={{ color: '#fff' }} />
        )}
      </MenuHandle>
    </>
  );
};

export default FlowMenu;
