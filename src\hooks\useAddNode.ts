import { GetNodeId } from '../utils/GetNodeId';
import type { BaseNodeData } from '../types/baseNode.types';

type NodeType = 'input' | 'agent' | 'tool' | 'output';



const nodePresets: Record<NodeType, { label: string }> = {
    input: { label: 'Input' },
    agent: { label: 'Agent' },
    tool:  { label: 'Tool' },
    output:{ label: 'Output' },
  };


  export function useAddNode(addToState: (node: BaseNodeData) => void) {
    const handleAddNode = (type: NodeType) => {
      const { label } = nodePresets[type];
  
      const newNode: BaseNodeData = {
        NodeId: GetNodeId(),
        NodeType: type,
        label,
        position: { x: 100, y: 100 },
        accentColor: '#ccc', // valfri default
      };
  
      addToState(newNode);
    };
  
    return handleAddNode;
  }
  