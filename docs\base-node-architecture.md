# BaseNode, BaseNodeData & Flow Node Architecture

## 1. What is `BaseNodeData`?
- **TypeScript interface** describing the shape of all node data in the flow system.
- Ensures every node has: `NodeId`, `NodeType`, `label`, `position`, and allows for styling, event handlers, children, and custom fields.
- Example usage:
  ```ts
  const node: BaseNodeData = {
    NodeId: 'abc',
    NodeType: 'agent',
    label: 'Agent',
    position: { x: 100, y: 200 },
    Output: 'Result',
  };
  ```

## 2. What is `BaseNode`?
- **React component** that renders the UI for a node.
- Receives props (like `NodeId`, `NodeType`, `label`, etc.) that match `BaseNodeData`.
- Handles layout, drag events, and shared node visuals.

## 3. Specialized Nodes (InputNode, AgentNode, ToolNode, OutputNode)
- Each is a React component for a specific node type.
- Example:
  ```tsx
  // InputNode.tsx
  export const InputNode = (props) => (
    <BaseNode {...props} NodeType="input" label="Input" />
  );
  ```
- They wrap `BaseNode` and pass their props (typed with `BaseNodeData`) to it.

## 4. How Data Flows & Is Used
- All node data in state is typed as `BaseNodeData`.
- When a node (e.g. AgentNode) executes logic, its output is stored in its `BaseNodeData` object (e.g. in the `Output` field).
- If nodes are connected, the output can be routed using the `OutputDestination` or similar fields.
- The UI (e.g. OutputNode) can read another node's output by following these links.

## 5. Example: AgentNode Producing Output
```ts
// Update agent node output
const agentNode = nodes.find(n => n.NodeId === 'agent-123');
if (agentNode) {
  agentNode.Output = 'Result from agent';
  agentNode.OutputDestination = 'output-456';
  setNodes([...nodes]);
}
```

## 6. Summary Table
| Concept         | What it is         | Purpose                                  |
|-----------------|-------------------|------------------------------------------|
| `BaseNodeData`  | TypeScript type   | Describes the shape of node data objects |
| `BaseNode`      | React component   | Renders a node visually and interactively|
| Specialized Nodes | React components | Render specific node types, use BaseNode |

## 7. Extending & Customizing
- You can add custom fields to `BaseNodeData` for special node logic or data.
- Specialized nodes can add unique UI or logic before/after rendering `BaseNode`.

## 8. Key Points
- `BaseNodeData` = data structure/type for all nodes.
- `BaseNode` = shared UI component for all nodes.
- Specialized nodes = wrappers for specific node types, use `BaseNode` for UI.
- Data (like output) is stored in the node's `BaseNodeData` object and can be routed between nodes.

---

**This doc summarizes how node data, node components, and data flow work together in your flow system.**
