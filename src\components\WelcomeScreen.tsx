import type { FC } from 'react';
import { styled } from '@mui/material/styles';

interface WelcomeScreenProps {
  onStartChat: () => void;
}

const WelcomeScreenContainer = styled('div')({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  height: '100%',
  padding: 20,
  textAlign: 'center',
});

const WelcomeTitle = styled('h1')({
  marginBottom: 10,
});

const WelcomeDescription = styled('p')({
  maxWidth: 600,
  marginBottom: 40,
  color: '#aaa',
});

const OptionsContainer = styled('div')({
  display: 'flex',
  gap: 20,
});

const OptionCard = styled('div')({
  backgroundColor: '#222',
  border: '1px solid #333',
  borderRadius: 8,
  padding: 20,
  width: 250,
  cursor: 'pointer',
  transition: 'background-color 0.2s',
  '&:hover': {
    backgroundColor: '#2a2a2a',
  },
});

const OptionIcon = styled('div')({
  fontSize: 24,
  marginBottom: 15,
});

const OptionCardTitle = styled('h3')({
  marginBottom: 10,
});

const OptionCardDescription = styled('p')({
  fontSize: 14,
  color: '#aaa',
  margin: 0,
});

const WelcomeScreen: FC<WelcomeScreenProps> = ({ onStartChat }) => {
  return (
    <WelcomeScreenContainer>
      <WelcomeTitle>Welcome to TailReact Chat</WelcomeTitle>
      <WelcomeDescription>Select one of the following CHAT options to start chatting with AI. Use AGENT NETWORK to connect automation solutions.</WelcomeDescription>
      <OptionsContainer>
        <OptionCard onClick={onStartChat}>
          <OptionIcon>💬</OptionIcon>
          <OptionCardTitle>Chats</OptionCardTitle>
          <OptionCardDescription>Start and manage individual AI chats with advanced features.</OptionCardDescription>
        </OptionCard>
        <OptionCard>
          <OptionIcon>🔄</OptionIcon>
          <OptionCardTitle>Agent Network</OptionCardTitle>
          <OptionCardDescription>Connect agents/models by linking agents together.</OptionCardDescription>
        </OptionCard>
      </OptionsContainer>
    </WelcomeScreenContainer>
  );
};

export default WelcomeScreen;