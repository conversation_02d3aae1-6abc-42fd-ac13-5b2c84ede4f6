---
description: Advanced React Codebase Analysis Checklist For @. 
---

1. 🏗️ Modularity
 Components are small, focused, and reusable
Fix: Split large components into smaller, single-responsibility components.
 Project structure follows best practices (see mapstructure)
Fix: Move files to appropriate directories (components/, hooks/, contexts/, etc.).
 No logic inside UI components (unless explicitly required)
Fix: Move logic to custom hooks or service files.
 Dependency injection is used where appropriate
Fix: Pass dependencies as props or via context, not by direct imports.
2. 👁️ Readability
 Consistent and descriptive naming conventions
Fix: Rename variables, functions, and files for clarity.
 Files and components are not excessively long
Fix: Refactor or split files/components.
 Code is well-commented and documented where necessary
Fix: Add JSDoc or inline comments for complex sections.
 Consistent formatting (indentation, spacing, etc.)
Fix: Use Prettier or similar formatter.
3. ⚛️ React Best Practices
 Uses functional components and hooks
Fix: Convert class components to functional components.
 Minimizes prop drilling (uses context/store when needed)
Fix: Refactor to use React Context or a state manager.
 No direct DOM manipulation (except via refs)
Fix: Use React refs or state, not document.getElementById, etc.
 Proper use of list keys and effect cleanup
Fix: Add unique keys, clean up effects in useEffect.
 No logic in UI-only components
Fix: Move logic to hooks or services.
4. 🧩 Complex Functions
 No large or deeply nested functions/components
Fix: Break down into smaller functions/components.
 No large inline functions inside JSX
Fix: Move logic outside of JSX or into hooks.
 Avoids excessive props or state
Fix: Simplify component interfaces.
5. 🧹 General Suggestions
 Run ESLint with React/recommended rules
Fix: Address all reported lint issues.
 Run Prettier for consistent formatting
Fix: Format all files.
 Add or update README with structure and conventions
Fix: Document project structure and best practices.