import React from 'react';
import { styled } from '@mui/material/styles';
import Popover from '@mui/material/Popover';
import MenuList from '@mui/material/MenuList';
import MenuItem from '@mui/material/MenuItem';
import BuildIcon from '@mui/icons-material/Build';
import SmartToyIcon from '@mui/icons-material/SmartToy';

/**
 * NodePopupMenu displays a themed popup menu near a node configuration button.
 * The menu adapts its appearance and options based on the node type.
 *
 * Props:
 * - anchorEl: The element to anchor the popup to (button ref)
 * - open: Boolean to control visibility
 * - onClose: Called when the menu should close
 * - nodeType: The type of node (e.g., 'ToolNode', 'ChatNode', etc)
 */
interface NodePopupMenuProps {
  anchorEl: HTMLElement | null;
  open: boolean;
  onClose: () => void;
  nodeType: string;
}

const PopupContainer = styled('div')<{ nodeType: string }>(({ nodeType, theme }) => ({
  minWidth: 200,
  background: nodeType === 'ToolNode'
    ? 'linear-gradient(120deg, #232a3a 60%, #4e8cff 100%)'
    : 'rgba(30, 32, 38, 0.92)',
  color: '#f3f6fa',
  borderRadius: 12,
  boxShadow: '0 4px 24px 0 rgba(0,0,0,0.16)',
  padding: theme.spacing(1, 0),
  border: '1.5px solid #3a3e48',
  backdropFilter: 'blur(10px)',
}));

const NodePopupMenu: React.FC<NodePopupMenuProps> = ({ anchorEl, open, onClose, nodeType }) => {
  return (
    <Popover
      open={open}
      anchorEl={anchorEl}
      onClose={onClose}
      anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      transformOrigin={{ vertical: 'top', horizontal: 'right' }}
      PaperProps={{ sx: { background: 'none', boxShadow: 'none' } }}
    >
      <PopupContainer nodeType={nodeType}>
        <MenuList>
          {nodeType === 'ToolNode' ? (
            <>
              <MenuItem>
                <BuildIcon fontSize="small" sx={{ mr: 1 }} /> Tool Settings
              </MenuItem>
              <MenuItem>
                <SmartToyIcon fontSize="small" sx={{ mr: 1 }} /> Tool Info
              </MenuItem>
            </>
          ) : (
            <>
              <MenuItem>
                <SmartToyIcon fontSize="small" sx={{ mr: 1 }} /> Node Settings
              </MenuItem>
              <MenuItem>
                <BuildIcon fontSize="small" sx={{ mr: 1 }} /> Node Info
              </MenuItem>
            </>
          )}
        </MenuList>
      </PopupContainer>
    </Popover>
  );
};

export default NodePopupMenu;
