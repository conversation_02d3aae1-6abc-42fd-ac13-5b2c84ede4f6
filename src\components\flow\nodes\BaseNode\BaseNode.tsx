import React from 'react';
import { styled } from '@mui/material/styles';
import { NodeSettingsButton } from './NodeSettingsButton';
import type { BaseNodeData } from '../../../../types/baseNode.types';

// Ensure we have proper type safety for the position prop
interface Position {
  x: number;
  y: number;
}

interface NodeContainerProps {
  $accentColor?: string;
}

const NodeContainer = styled('div')<NodeContainerProps>(({ theme, $accentColor }) => ({
  position: 'absolute',
  width: 220,
  height: 80,
  background: 'rgba(30,40,60,0.45)',
  border: `2.5px solid ${$accentColor || '#5b9cff'}`,
  borderRadius: 24,
  boxShadow: `0 4px 32px 0 ${$accentColor || '#5b9cff'}33, 0 1.5px 0 #0a1f3c`,
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'stretch',
  justifyContent: 'flex-start',
  fontWeight: 600,
  color: '#e9f0fb',
  cursor: 'grab',
  userSelect: 'none',
  fontFamily: 'Inter, Segoe UI, Arial, sans-serif',
  transition: 'box-shadow 0.2s, border 0.2s',
  backdropFilter: 'blur(10px) saturate(180%)',
  overflow: 'hidden',
  ':hover': {
    boxShadow: `0 8px 36px 0 ${$accentColor || '#3578e5'}44, 0 1.5px 0 #3578e5`,
    border: `2.5px solid ${$accentColor || '#3578e5'}`,
  },
}));

// Define props for the BaseNode component
type BaseNodeProps = {
  NodeId: string;
  NodeType: BaseNodeData['NodeType'];
  label: string;
  accentColor?: string;
  position?: Position;
  children?: React.ReactNode;
  onMouseDown: (e: React.MouseEvent, id: string) => void;
  onSettingsClick?: (event: React.MouseEvent<HTMLElement>, nodeType: string) => void;
  style?: React.CSSProperties;
  [key: string]: unknown; // Allow additional props
};

export const BaseNode: React.FC<BaseNodeProps> = ({
  NodeId,
  NodeType,
  label,
  accentColor = '#5b9cff',
  position = { x: 0, y: 0 },
  children,
  onMouseDown,
  onSettingsClick,
  style
}) => {
  const containerStyle: React.CSSProperties = {
    left: position?.x ?? 0, 
    top: position?.y ?? 0,
    ...(style || {})
  };

  return (
    <NodeContainer 
      $accentColor={accentColor} 
      style={containerStyle} 
      onMouseDown={e => onMouseDown(e, NodeId)}
    >
      {/* Node header */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%',
        padding: '10px 16px 4px 16px',
        boxSizing: 'border-box',
        borderBottom: '1.5px solid rgba(90,140,255,0.08)',
        background: 'linear-gradient(90deg, rgba(60,100,180,0.12) 0%, rgba(60,120,255,0.10) 100%)',
        fontSize: 16,
        fontWeight: 700,
        letterSpacing: 0.3,
        textShadow: '0 1px 2px #0a1f3c22',
        color: '#eaf6ff',
      }}>
        <span style={{ fontSize: 16, fontWeight: 700, letterSpacing: 0.3, color: '#eaf6ff', textShadow: '0 1px 2px #0a1f3c22' }}>
          {label}
        </span>
        <div style={{ position: 'relative', zIndex: 2 }}>
          <NodeSettingsButton 
            nodeType={NodeType}
            onSettingsClick={onSettingsClick || ((event) => {})}
          />
        </div>
      </div>
      {/* Node body */}
      <div style={{
        flex: 1,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '0 16px',
        color: '#b5c8e8',
        fontSize: 13,
        background: 'transparent',
        position: 'relative',
        minHeight: 40,
      }}>
        {children}
      </div>
    </NodeContainer>
  );
}
