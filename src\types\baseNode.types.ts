

import type { <PERSON><PERSON>N<PERSON>, MouseEvent, CSSProperties } from 'react';

// Base type for all node data in the application
export interface BaseNodeData {
  // Required properties for all nodes
  NodeId: string;
  NodeType: 'input' | 'agent' | 'tool' | 'output';
  label: string;
  position: { x: number; y: number };
  
  // Styling
  accentColor?: string;
  className?: string;
  style?: CSSProperties;
  
  // Event handlers
  onMouseDown?: (e: MouseEvent, id: string) => void;
  onSettingsClick?: (e: MouseEvent) => void;
  
  // Children
  children?: ReactNode;
  
  // Node-specific properties
  Input?: string;
  SourceNode?: string;
  Output?: string;
  OutputDestination?: string;
  
  // Any other additional properties
  [key: string]: unknown;
}

// Type guard to check if an object is a valid BaseNodeData
export function isBaseNodeData(data: unknown): data is BaseNodeData {
  if (typeof data !== 'object' || data === null) return false;
  const node = data as Record<string, unknown>;
  return (
    typeof node.NodeId === 'string' &&
    typeof node.NodeType === 'string' &&
    typeof node.label === 'string' &&
    typeof node.position === 'object' &&
    node.position !== null &&
    typeof (node.position as { x: unknown }).x === 'number' &&
    typeof (node.position as { y: unknown }).y === 'number'
  );
}


