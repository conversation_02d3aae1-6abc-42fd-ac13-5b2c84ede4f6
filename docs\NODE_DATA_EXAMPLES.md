# Exempel: BaseNodeData för olika noder

Här visas hur du kan fylla i BaseNodeData för olika typer av noder i din flow-app:

```ts
// Exempel för en AgentNode
const agentNodeData: BaseNodeData = {
  NodeType: 'AgentNode',
  Input: 'Hej!',
  SourceNode: 'InputNode',
  Output: 'Hej! Hur kan jag hjälpa dig idag?',
  OutputDestination: 'OutputNode',
};

// Exempel för en InputNode
const inputNodeData: BaseNodeData = {
  NodeType: 'InputNode',
  Output: 'Hej!',
  OutputDestination: 'AgentNode',
};

// Exempel för en OutputNode
const outputNodeData: BaseNodeData = {
  NodeType: 'OutputNode',
  SourceNode: 'AgentNode',
  Input: 'Hej! Hur kan jag hjälpa dig idag?',
};
```

Du kan använda BaseNodeData för att tydligt beskriva och strukturera dataflödet mellan dina noder.

> **Tips:** Du kan lägga till fler fält i interfacet om du behöver mer information för dina noder!
