import type { FC } from 'react';
import { useState } from 'react';
import { styled } from '@mui/material/styles';

const MessageInput: FC = () => {
  const [message, setMessage] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim()) {
      // Här skulle du skicka meddelandet
      console.log('Sending message:', message);
      setMessage('');
    }
  };

  const MessageInputContainer = styled('div')({
    padding: 15,
    borderTop: '1px solid #333',
  });

  const MessageForm = styled('form')({
    display: 'flex',
    gap: 10,
  });

  const MessageInputField = styled('input')({
    flex: 1,
    padding: '12px 15px',
    borderRadius: 20,
    border: '1px solid #444',
    backgroundColor: '#222',
    color: 'white',
    outline: 'none',
  });

  const SendButton = styled('button')({
    width: 40,
    height: 40,
    borderRadius: '50%',
    backgroundColor: '#0078d4',
    color: 'white',
    border: 'none',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
  });

  return (
    <MessageInputContainer>
      <MessageForm onSubmit={handleSubmit}>
        <MessageInputField
          type="text"
          placeholder="Type your message..."
          value={message}
          onChange={(e) => setMessage(e.target.value)}
        />
        <SendButton type="submit">
          <span>➤</span>
        </SendButton>
      </MessageForm>
    </MessageInputContainer>
  );
};

export default MessageInput;