import type { FC } from 'react';
import { styled } from '@mui/material/styles';

interface MessageProps {
  text: string;
  isUser: boolean;
  time: string;
}

const MessageContainer = styled('div')<{ isUser?: boolean }>(({ isUser }) => ({
  display: 'flex',
  maxWidth: '70%',
  alignSelf: isUser ? 'flex-end' : 'flex-start',
}));

const MessageBubble = styled('div')<{ isUser?: boolean }>(({ isUser }) => ({
  padding: '10px 15px',
  borderRadius: 18,
  position: 'relative',
  backgroundColor: isUser ? '#0078d4' : '#333',
  color: 'white',
  borderBottomRightRadius: isUser ? 5 : 18,
  borderBottomLeftRadius: isUser ? 18 : 5,
}));

const MessageText = styled('div')({});

const MessageTime = styled('div')({
  fontSize: 10,
  color: '#aaa',
  textAlign: 'right',
  marginTop: 4,
});

const Message: FC<MessageProps> = ({ text, isUser, time }) => {
  return (
    <MessageContainer isUser={isUser}>
      <MessageBubble isUser={isUser}>
        <MessageText>{text}</MessageText>
        <MessageTime>{time}</MessageTime>
      </MessageBubble>
    </MessageContainer>
  );
};

export default Message;