import React from 'react';
import { ReactFlowBaseNode, type ReactFlowBaseNodeProps } from './ReactFlowBaseNode';

export const ReactFlowOutputNode: React.FC<ReactFlowBaseNodeProps> = (props) => {
  const nodeData = {
    ...props.data,
    label: 'Output',
    accentColor: '#ff6978',
    NodeType: 'output' as const
  };

  return (
    <ReactFlowBaseNode
      data={nodeData}
    >
      {/* Output node specific content can go here */}
    </ReactFlowBaseNode>
  );
};
