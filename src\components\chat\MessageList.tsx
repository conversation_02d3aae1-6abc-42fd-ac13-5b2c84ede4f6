import type { FC } from 'react';
import { styled } from '@mui/material/styles';
import Message from './Message';

const MessageList: FC = () => {
  const messages = [
    { id: 1, text: "Hello! How are you? How can I help you today?", sender: "agent", time: "16:25" },
    { id: 2, text: "hello", sender: "user", time: "16:26" },
    { id: 3, text: "I'm wondering how I can help you today?", sender: "agent", time: "16:27" },
    { id: 4, text: "hello", sender: "user", time: "16:27" }
  ];

  const MessageListContainer = styled('div')({
    flex: 1,
    overflowY: 'auto',
    padding: 20,
    display: 'flex',
    flexDirection: 'column',
    gap: 15,
  });

  return (
    <MessageListContainer>
      {messages.map(message => (
        <Message 
          key={message.id}
          text={message.text}
          isUser={message.sender === 'user'}
          time={message.time}
        />
      ))}
    </MessageListContainer>
  );
};

export default MessageList;