import React from 'react';
import { BaseNode } from './BaseNode/BaseNode';
import type { BaseNodeData } from '../../../types/baseNode.types';

interface OutputNodeProps extends Omit<BaseNodeData, 'NodeType' | 'label' | 'accentColor'> {
  NodeId: string;
  position: { x: number; y: number };
  onMouseDown: (e: React.MouseEvent, id: string) => void;
}

export const OutputNode: React.FC<OutputNodeProps> = ({
  NodeId,
  position,
  onMouseDown,
  ...props
}) => {
  return (
    <BaseNode
      NodeId={NodeId}
      position={position}
      onMouseDown={onMouseDown}
      NodeType="output"
      label="Output"
      accentColor="#ff6978"
      {...props}
    />
  );
};

// NodeId kommer nu endast från BaseNodeData och dupliceras inte längre i OutputNodeProps.
