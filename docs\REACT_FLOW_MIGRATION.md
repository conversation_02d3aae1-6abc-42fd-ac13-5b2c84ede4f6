# React Flow Migration Guide

## Overview
This document describes the migration from the custom flow implementation to React Flow library.

## What Changed

### Removed Files
The following files were removed as they are no longer needed:
- `src/components/flow/FlowWindow.tsx` → Replaced by `ReactFlowWindow.tsx`
- `src/components/flow/FlowSVG.tsx` → React Flow handles connections
- `src/components/flow/nodes/InputNode.tsx` → Replaced by React Flow versions
- `src/components/flow/nodes/AgentNode.tsx` → Replaced by React Flow versions
- `src/components/flow/nodes/ToolNode.tsx` → Replaced by React Flow versions
- `src/components/flow/nodes/OutputNode.tsx` → Replaced by React Flow versions
- `src/components/flow/nodes/BaseNode/BaseNode.tsx` → Replaced by React Flow versions
- `src/hooks/useFlowNodes.ts` → Replaced by React Flow hooks
- `src/hooks/useFlowWindow.ts` → Replaced by React Flow functionality
- `src/hooks/useAddNode.ts` → Replaced by `useReactFlowNodes.ts`

### New Files
- `src/components/flow/ReactFlowWindow.tsx` - Main React Flow component
- `src/components/flow/nodes/ReactFlowNodes/` - React Flow compatible node components
  - `ReactFlowBaseNode.tsx` - Base node component for React Flow
  - `ReactFlowInputNode.tsx` - Input node for React Flow
  - `ReactFlowAgentNode.tsx` - Agent node for React Flow
  - `ReactFlowToolNode.tsx` - Tool node for React Flow
  - `ReactFlowOutputNode.tsx` - Output node for React Flow
  - `index.ts` - Exports and node types mapping
- `src/hooks/useReactFlowNodes.ts` - React Flow specific hooks
- `src/utils/reactFlowConverters.ts` - Utilities for converting between formats

### Kept Files
- `src/components/flow/nodes/BaseNode/NodeSettingsButton.tsx` - Still used by React Flow nodes
- `src/components/flow/nodes/BaseNode/NodePopupMenu.tsx` - Still used by React Flow nodes
- `src/components/flow/FlowMenu/FlowMenu.tsx` - Updated to work with React Flow
- `src/types/baseNode.types.ts` - Still used for data compatibility

## Key Features

### React Flow Benefits
1. **Professional drag & drop** - Built-in smooth dragging and positioning
2. **Connection handling** - Automatic edge creation and management
3. **Zoom and pan** - Built-in viewport controls
4. **Minimap** - Overview of the entire flow
5. **Background grid** - Visual grid for better alignment
6. **Performance** - Optimized for large flows
7. **Accessibility** - Built-in keyboard navigation

### Maintained Compatibility
- All existing `BaseNodeData` types are preserved
- Node styling matches the original design
- FlowMenu integration works seamlessly
- Same node types: input, agent, tool, output

### New Capabilities
- **Smooth connections** - Curved edges with arrow markers
- **Visual feedback** - Handles highlight during connection
- **Better UX** - Professional flow editing experience
- **Extensibility** - Easy to add new node types and features

## Usage

### Adding Nodes
```typescript
// FlowMenu automatically calls this when user clicks node type
const addNode = (type: string) => {
  // Converts string to NodeType and adds to React Flow
};
```

### Node Structure
```typescript
// React Flow nodes maintain BaseNodeData compatibility
interface ReactFlowNodeData extends BaseNodeData {
  label: string;
  accentColor?: string;
}
```

### Connections
- Drag from output handle (right) to input handle (left)
- Connections are automatically managed by React Flow
- Edge styling matches original design

## Migration Benefits

1. **Reduced Code** - Eliminated ~1000 lines of custom flow logic
2. **Better UX** - Professional drag & drop experience
3. **Maintainability** - Using established library instead of custom code
4. **Features** - Built-in zoom, pan, minimap, and more
5. **Performance** - Optimized rendering for large flows
6. **Future-proof** - Active library with regular updates

## Next Steps

1. Test the new implementation thoroughly
2. Add any missing custom features if needed
3. Consider adding React Flow plugins for additional functionality
4. Update documentation for end users
