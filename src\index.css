/* React Flow CSS Overrides */
.react-flow__node {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  border-radius: 0 !important;
  box-shadow: none !important;
}

.react-flow__node-input,
.react-flow__node-agent,
.react-flow__node-tool,
.react-flow__node-output {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  border-radius: 0 !important;
  box-shadow: none !important;
}

.react-flow__node.selected {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

/* Ensure handles are properly positioned and interactive */
.react-flow__handle {
  position: absolute !important;
  z-index: 1000 !important;
  pointer-events: all !important;
  cursor: crosshair !important;
  width: 20px !important;
  height: 20px !important;
  border-radius: 50% !important;
  background: #6db6ff !important;
  border: 3px solid #ffffff !important;
  transition: all 0.2s ease !important;
}

.react-flow__handle:hover {
  background: #2ec4ff !important;
  transform: scale(1.1) !important;
  box-shadow: 0 0 10px rgba(46, 196, 255, 0.6) !important;
}

.react-flow__handle.react-flow__handle-connecting {
  background: #2ec4ff !important;
  transform: scale(1.2) !important;
  box-shadow: 0 0 15px rgba(46, 196, 255, 0.8) !important;
}

/* Prevent node movement when clicking handles */
.react-flow__handle {
  pointer-events: all !important;
}

.react-flow__node.nopan .react-flow__handle {
  pointer-events: all !important;
}