import React from 'react';
import { React<PERSON>lowBaseNode, type ReactFlowBaseNodeProps } from './ReactFlowBaseNode';

export const ReactFlowInputNode: React.FC<ReactFlowBaseNodeProps> = (props) => {
  const nodeData = {
    ...props.data,
    label: 'Input',
    accentColor: '#4e8cff',
    NodeType: 'input' as const
  };

  return (
    <ReactFlowBaseNode
      data={nodeData}
    >
      {/* Input node specific content can go here */}
    </ReactFlowBaseNode>
  );
};
