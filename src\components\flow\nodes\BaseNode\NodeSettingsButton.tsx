import React from 'react';
import { styled } from '@mui/material/styles';
import SettingsIcon from '@mui/icons-material/Settings';


const Button = styled('button')({
  position: 'absolute',
  top: 6,
  right: 8,
  width: 28,
  height: 28,
  background: 'rgba(255,255,255,0.82)',
  border: 'none',
  borderRadius: '50%',
  boxShadow: '0 2px 8px 0 rgba(78,140,255,0.10)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  cursor: 'pointer',
  transition: 'background 0.18s, box-shadow 0.18s, opacity 0.12s',
  opacity: 0.88,
  zIndex: 2,
  padding: 0,
  ':hover': {
    background: 'linear-gradient(135deg, #3578e5 80%, #1e2a43 100%)',
    color: '#fff',
    opacity: 1,
  },
});

const Icon = styled(SettingsIcon)({
  fontSize: 18,
  color: '#345',
});

interface NodeSettingsButtonProps {
  nodeType: string;
  onSettingsClick: (event: React.MouseEvent<HTMLElement>, nodeType: string) => void;
}

export const NodeSettingsButton: React.FC<Omit<React.ComponentProps<'button'>, 'onClick'> & NodeSettingsButtonProps> = ({ nodeType, onSettingsClick, ...buttonProps }) => (
  <Button 
    {...buttonProps} 
    onClick={(e) => onSettingsClick(e, nodeType)}
    aria-label={`${nodeType} settings`}
  >
    <Icon />
  </Button>
);
