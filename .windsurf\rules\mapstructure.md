---
trigger: always_on
---

components/
Innehåll: Återanvändbara UI-komponenter, t.ex. Button.jsx, Navbar.jsx, ChatMessage.jsx
Tänk: Allt som ritas upp och kan återanvändas

🔹 pages/ (om Next.js eller liknande)
Innehåll: Varje fil representerar en sida i appen.
Tänk: Route-baserad rendering – about.js → /about

🔹 services/
Innehåll: API-anrop, externa fetch-funktioner, t.ex. chatService.js
Tänk: Pratar med backend eller externa system

🔹 contexts/
Innehåll: React Contexts – t.ex. AuthContext.jsx, ThemeContext.jsx
Tänk: Delar global state mellan komponenter utan props

🔹 hooks/
Innehåll: Egna hooks – t.ex. useChatMessages.js, useFetch.js
Tänk: Styr logiken i komponenter. Återanvänd logik i komponenter, istället för att duplicera

🔹 store/ (vid t.ex. Redux eller Zustand)
Innehåll: Global state management, actions, reducers
Tänk: En "central hjärna" för data i hela appen

🔹 assets/
Innehåll: Bilder, fonts, ikoner osv.
Tänk: Statiska filer

🔹 styles/
Innehåll: CSS, Tailwind config, SCSS, eller styled-components
Tänk: Allt som rör utseende

🔹 utils/ eller lib/
Innehåll: Små hjälpfunktioner – t.ex. formatDate.js, validateEmail.js
Tänk: Logik som inte är React-specifik, kan användas var som helst

🔹 types/ (om TypeScript)
Innehåll: Type-definitioner och interfaces
Tänk: Beskriver strukturen på data

🔹 config/
Innehåll: Konfiguration – t.ex. API-URL, miljövariabler
Tänk: Inställningar för hela appen