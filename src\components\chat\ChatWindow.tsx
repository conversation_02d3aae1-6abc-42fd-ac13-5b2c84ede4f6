import type { FC } from 'react';
import { useState } from 'react';
import { styled } from '@mui/material/styles';
import WelcomeScreen from '../WelcomeScreen';
import MessageList from './MessageList';
import MessageInput from './MessageInput';

const ChatWindow: FC = () => {
  const [activeChat, setActiveChat] = useState<boolean>(false);
  
  // For demo, toggle between welcome screen and chat screen
  const toggleChat = () => {
    setActiveChat(!activeChat);
  };

  const ChatWindowContainer = styled('div')({
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    position: 'relative',
  });

  const ChatHeader = styled('div')({
    padding: 15,
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottom: '1px solid #333',
  });

  const SimulateReplyButton = styled('button')({
    backgroundColor: '#0078d4',
    color: 'white',
    border: 'none',
    padding: '8px 15px',
    borderRadius: 4,
    cursor: 'pointer',
  });

  return (
    <ChatWindowContainer>
      {activeChat ? (
        <>
          <ChatHeader>
            <h3>KalleBalleHangkuk</h3>
            <SimulateReplyButton onClick={toggleChat}>SIMULATE REPLY</SimulateReplyButton>
          </ChatHeader>
          <MessageList />
          <MessageInput />
        </>
      ) : (
        <WelcomeScreen onStartChat={toggleChat} />
      )}
    </ChatWindowContainer>
  );
};

export default ChatWindow;